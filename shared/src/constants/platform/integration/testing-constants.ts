/**
 * @file Integration Testing Constants
 * @filepath shared/src/constants/platform/integration/testing-constants.ts
 * @description Constants for integration testing framework components
 */

// Load Testing Constants
export const DEFAULT_LOAD_TEST_TIMEOUT = 300000; // 5 minutes
export const DEFAULT_PERFORMANCE_MONITORING_INTERVAL = 30000; // 30 seconds
export const DEFAULT_MEMORY_CLEANUP_INTERVAL = 60000; // 1 minute
export const MAX_CONCURRENT_LOAD_TESTS = 10;
export const LOAD_TEST_COORDINATION_INTERVAL = 15000; // 15 seconds

// Performance Constants
export const DEFAULT_RESPONSE_TIME_THRESHOLD = 10; // 10ms
export const DEFAULT_THROUGHPUT_THRESHOLD = 1000; // requests per second
export const DEFAULT_ERROR_RATE_THRESHOLD = 0.01; // 1%
export const DEFAULT_MEMORY_THRESHOLD = 100; // MB

// Monitoring Constants
export const DEFAULT_METRICS_COLLECTION_INTERVAL = 5000; // 5 seconds
export const DEFAULT_HEALTH_CHECK_INTERVAL = 30000; // 30 seconds
export const DEFAULT_ALERTING_THRESHOLD = 0.8; // 80%

// Test Configuration Constants
export const DEFAULT_TEST_TIMEOUT = 120000; // 2 minutes
export const DEFAULT_STRESS_TEST_DURATION = 300000; // 5 minutes
export const DEFAULT_BASELINE_MEASUREMENT_DURATION = 60000; // 1 minute

// Resource Limits
export const MAX_LOAD_TEST_HISTORY = 1000;
export const MAX_PERFORMANCE_BASELINES = 50;
export const MAX_MONITORING_SESSIONS = 100;
export const MAX_SCHEDULED_TESTS = 200;

// Cleanup Constants
export const HISTORY_CLEANUP_INTERVAL = 300000; // 5 minutes
export const BASELINE_CLEANUP_INTERVAL = 3600000; // 1 hour
export const SESSION_CLEANUP_INTERVAL = 1800000; // 30 minutes
